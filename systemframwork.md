| 环节                 | 推荐模型                          | 模型大小     | 显存占用 |
| -------------------- | --------------------------------- | ------------ | -------- |
| ① 说话人分离         | Pyannote.audio                    | <100MB       | CPU      |
| ② ASR转写            | **Whisper-large-v3** (原为medium) | 约2GB FP16   | ≈2GB     |
| ③ 声学情绪分类       | emotion2vec-base                  | 约300MB FP16 | ≈0.5GB   |
| ④ 文本情绪分类+NER   | **XLM-R-large (原为base)**        | 约1.3GB FP16 | ≈1.3GB   |
| ⑤ 病历标准检查与反馈 | 规则匹配与模板反馈（非LLM）       | 极小         | CPU      |

## 🧩 不使用LLM的反馈生成算法（推荐）

你既然有明确的**病历写入标准手册**，建议采用：

1. **规则库设计**：

   - 提前提取**标准手册**中的病历必须填写的要素（如用药史、过敏史、主诉、既往史、现病史等）；
   - 设定**每种病种或每个场景**（如糖尿病初诊、心血管初诊）需要询问的**关键项目清单**。

2. **检查算法实现**：

   - 使用XLM-R-large模型做NER，从转录文本提取诊疗环节中的关键信息；
   - 按照规则库**自动核对是否缺失**，若缺失，则触发反馈模板。

3. **反馈模板设计**：

   - 每一条规则预定义一个反馈模板，如：

     ```
     markdown
     - 问题：未询问患者药物使用情况
     - 反馈：『根据《广东省电子病历书写规范》第XX条，初诊患者病历需明确填写患者当前用药情况，请医生注意记录。』
     ```

4. **生成机制**：

   - 若规则触发，直接用模板合成反馈，**简单、高效、准确性高、可解释性强**。

> 这样做无需LLM，且反馈精准明确，适合医疗场景的严谨要求。

------

## ⚡ 实时demo技巧详细整合到方案中：

为快速构建医院能直观看到效果的实时演示：

- **实时切片处理**：
   将输入的长音频（如10分钟）切分为短音频片段（如10秒），并在网页端实时展示处理结果（如情绪曲线、转录结果随音频实时更新）。
- **前端交互界面**：
   可用Streamlit或Gradio快速搭建Demo界面，实时显示：
  - **医患对话文本**（ASR输出）
  - **角色情绪状态变化曲线**（图表化）
  - **漏项或错误的实时反馈**（根据规则库）
  - **整体规范性评估得分**（基于标准规则匹配的数量）